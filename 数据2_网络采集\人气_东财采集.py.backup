#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import time
import random
from pathlib import Path
from datetime import datetime
from typing import Optional
from playwright.sync_api import sync_playwright, Playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

# 必须先设置路径再导入TradeFusion模块
def get_project_paths():
    """获取项目路径配置并设置Python路径"""
    script_path = Path(__file__).absolute()
    project_root = script_path.parent.parent

    paths = {
        'root': project_root,
        'logs': project_root / 'logs',
        'crawler': project_root / '数据2_网络采集',
        'common': project_root / '公共模块'
    }

    # 确保目录存在
    for key, path_val in paths.items():
        if key in ['logs', 'crawler', 'common']:
            if not path_val.exists():
                path_val.mkdir(parents=True, exist_ok=True)

    # 设置Python路径
    if str(paths['root']) not in sys.path:
        sys.path.insert(0, str(paths['root']))
    if str(paths['common']) not in sys.path:
        sys.path.insert(0, str(paths['common']))

    return paths

PATHS = get_project_paths()

# 现在可以安全导入TradeFusion模块
from 公共模块.TradeFusion统一日志标准 import 获取日志器
logger = 获取日志器("人气_东财采集")

# ==================== 模块配置常量 ====================
# 东方财富采集配置 - 模块内部自管理，无外部依赖
class EastmoneyConfig:
    """东方财富采集配置常量"""
    # 目标网站配置
    TARGET_URL = 'https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock'

    # 浏览器配置
    BROWSER_TYPE = 'chromium'  # 浏览器类型：chromium, firefox, webkit
    HEADLESS = True            # 无头模式，提高性能和稳定性
    TIMEOUT = 60000           # 页面超时时间（毫秒）

    # 时间配置（秒）
    PAGE_WAIT_MIN = 2          # 页面等待最小时间
    PAGE_WAIT_MAX = 4          # 页面等待最大时间
    LOAD_WAIT = 3              # 页面加载等待时间
    OPERATION_WAIT_MIN = 1     # 操作间隔最小时间
    OPERATION_WAIT_MAX = 2     # 操作间隔最大时间
    CYCLE_INTERVAL = 60        # 循环执行间隔（秒）

    # 鼠标行为配置
    MOUSE_MOVE_MIN = 100       # 鼠标移动最小坐标
    MOUSE_MOVE_MAX = 500       # 鼠标移动最大坐标
    MOUSE_WHEEL_MIN = 300      # 滚轮滚动最小值
    MOUSE_WHEEL_MAX = 600      # 滚轮滚动最大值

    # 数据提取配置
    MIN_DATA_THRESHOLD = 10    # 最小数据量阈值，低于此值启用备用策略

class SimpleBrowserManager:
    """简化的浏览器管理器 - 原生Playwright实现，支持持久化连接"""

    # 类级别的共享实例，实现浏览器复用
    _shared_instance: Optional['SimpleBrowserManager'] = None

    def __init__(self):
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None

    @classmethod
    def get_shared_instance(cls):
        """获取共享的浏览器管理器实例"""
        if cls._shared_instance is None:
            cls._shared_instance = cls()
        return cls._shared_instance

    def start_browser(self):
        """启动浏览器"""
        try:
            if self.playwright is None:
                self.playwright = sync_playwright().start()

            if self.browser is None:
                # 根据配置选择浏览器类型
                if EastmoneyConfig.BROWSER_TYPE == 'firefox':
                    self.browser = self.playwright.firefox.launch(
                        headless=EastmoneyConfig.HEADLESS
                    )
                elif EastmoneyConfig.BROWSER_TYPE == 'webkit':
                    self.browser = self.playwright.webkit.launch(
                        headless=EastmoneyConfig.HEADLESS
                    )
                else:  # chromium (默认)
                    self.browser = self.playwright.chromium.launch(
                        headless=EastmoneyConfig.HEADLESS,
                        args=['--no-sandbox', '--disable-dev-shm-usage']
                    )

            if self.context is None:
                self.context = self.browser.new_context(
                    viewport={'width': 1920, 'height': 1080},
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                )

            if self.page is None:
                self.page = self.context.new_page()

            return True
        except Exception as e:
            logger.记录错误(f"浏览器启动失败", e)
            return False

    def get_page(self, url=None, force_refresh=False) -> Page:
        """获取页面"""
        if not self.start_browser():
            raise Exception("浏览器启动失败")

        # 确保page已创建
        if self.page is None:
            raise Exception("页面创建失败")

        if url:
            if self.page.url != url:
                self.page.goto(url, wait_until='domcontentloaded', timeout=EastmoneyConfig.TIMEOUT)
            elif force_refresh:
                self.page.reload(wait_until='domcontentloaded', timeout=EastmoneyConfig.TIMEOUT)

        return self.page

    def close(self):
        """清理页面状态，但保持浏览器开启以便复用"""
        # 不关闭浏览器，只是清理当前页面状态
        # 浏览器实例会被保留用于下次使用
        pass

    def force_close(self):
        """强制关闭浏览器（仅在程序退出时使用）"""
        try:
            if self.page:
                self.page.close()
                self.page = None
            if self.context:
                self.context.close()
                self.context = None
            if self.browser:
                self.browser.close()
                self.browser = None
            if self.playwright:
                self.playwright.stop()
                self.playwright = None
        except Exception:
            pass

    @classmethod
    def cleanup_shared_instance(cls):
        """清理共享实例（程序退出时调用）"""
        if cls._shared_instance:
            cls._shared_instance.force_close()
            cls._shared_instance = None

def save_debug_info(page, error_type, timestamp=None):
    """保存调试信息：截图和HTML源码"""
    try:
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 截图
        screenshot_path = PATHS['logs'] / f"eastmoney_{error_type}_{timestamp}.png"
        page.screenshot(path=str(screenshot_path))

        # 保存HTML源码
        html_content = page.content()
        html_path = PATHS['logs'] / f"eastmoney_{error_type}_{timestamp}.html"
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        logger.记录错误(f"已保存调试信息: {error_type}")
    except Exception:
        pass






# 现在可以安全导入其他模块
try:
    from 公共模块.交易日期 import get_trading_date
    from 公共模块.加代码前缀 import process_stock_code_prefix
except ImportError as e:
    logger.记录错误(f"导入模块失败", e)
    sys.exit(1)


def scrape_stock_codes():
    """抓取东方财富热榜A股页面的股票代码和名称"""





    # 获取共享的浏览器管理器实例（支持浏览器复用）
    browser_manager = SimpleBrowserManager.get_shared_instance()

    try:
        # 获取页面，自动处理浏览器创建和页面刷新
        page = browser_manager.get_page(EastmoneyConfig.TARGET_URL, force_refresh=True)

        # 等待页面稳定
        time.sleep(random.uniform(EastmoneyConfig.PAGE_WAIT_MIN, EastmoneyConfig.PAGE_WAIT_MAX))

    except Exception as nav_error:
        # 保存调试信息
        try:
            if browser_manager.page:
                save_debug_info(browser_manager.page, "nav_error")
        except:
            pass
        browser_manager.close()
        raise

    try:
        # 增加等待时间确保页面完全加载
        time.sleep(EastmoneyConfig.LOAD_WAIT)

        # 模拟用户行为
        page.mouse.move(
            random.randint(EastmoneyConfig.MOUSE_MOVE_MIN, EastmoneyConfig.MOUSE_MOVE_MAX),
            random.randint(EastmoneyConfig.MOUSE_MOVE_MIN, EastmoneyConfig.MOUSE_MOVE_MAX)
        )
        page.mouse.wheel(0, random.randint(EastmoneyConfig.MOUSE_WHEEL_MIN, EastmoneyConfig.MOUSE_WHEEL_MAX))
        time.sleep(random.uniform(EastmoneyConfig.PAGE_WAIT_MIN, EastmoneyConfig.PAGE_WAIT_MAX))

        page.wait_for_load_state("networkidle")
        time.sleep(EastmoneyConfig.LOAD_WAIT)  # 等待页面完全加载



        # 使用优化的JavaScript提取代码和名称
        stock_data = page.evaluate("""
                () => {
                    const seenCodes = new Set();
                    const stockData = [];

                    // 通用的股票数据验证和添加函数
                    const addStockData = (code, name) => {
                        // 处理带前缀的代码（去掉SZ/SH前缀）
                        const cleanCode = code.length > 6 ? code.substring(2) : code;

                        if (cleanCode && cleanCode.length === 6 && name && !seenCodes.has(cleanCode)) {
                            seenCodes.add(cleanCode);
                            stockData.push({code: cleanCode, name: name.trim()});
                        }
                    };

                    // 策略1：从DOM元素文本内容提取
                    document.querySelectorAll('.item').forEach(item => {
                        const codeElement = item.querySelector('.code');
                        const nameElement = item.querySelector('.stock-name span');

                        if (codeElement && nameElement) {
                            addStockData(codeElement.textContent.trim(), nameElement.textContent.trim());
                        }
                    });

                    // 策略2：从属性提取（如果策略1没有找到足够数据）
                    if (stockData.length < """ + str(EastmoneyConfig.MIN_DATA_THRESHOLD) + """) {
                        document.querySelectorAll('[stockcode][stockname]').forEach(el => {
                            const stockCode = el.getAttribute('stockcode');
                            const stockName = el.getAttribute('stockname');

                            if (stockCode && stockName) {
                                addStockData(stockCode, stockName);
                            }
                        });
                    }

                    return stockData;
                }
        """)

        # 如果没有提取到数据，保存页面快照和源码用于调试
        if len(stock_data) == 0:
            save_debug_info(page, "error")
            return []

        time.sleep(random.uniform(EastmoneyConfig.OPERATION_WAIT_MIN, EastmoneyConfig.OPERATION_WAIT_MAX))

        return stock_data

    except Exception as e:
        import traceback
        traceback.print_exc()
        return []
    finally:
        # 保持浏览器开启，不关闭以便复用
        browser_manager.close()  # 这里只是清理页面状态，不会真正关闭浏览器

def _trigger_popularity_fusion():
    """触发个股人气表融合模块"""
    try:
        # 导入人气融合模块
        from 数据2_网络采集.个股人气表 import main as 人气融合_main

        # logger.info(f"🔄 [人气_东财采集] 自动触发人气融合模块...")

        # 调用人气融合函数
        result = 人气融合_main()

        if result:
            # logger.info(f"✅ [人气_东财采集] 人气融合模块执行成功")
            pass
        else:
            logger.记录错误(f"人气融合模块执行失败")

    except Exception as e:
        logger.记录错误(f"触发人气融合模块异常", e)

def main():
    start_time = time.time()
    try:
        random.seed(time.time())

        # 移除开始日志，只保留完成日志

        raw_stock_data = scrape_stock_codes()

        if raw_stock_data:
            trading_date = get_trading_date()
            processed_data_for_temp_table = []

            for i, stock in enumerate(raw_stock_data, 1):
                code = stock['code']
                name = stock['name']
                processed_code = process_stock_code_prefix(code)
                # rank is i
                processed_data_for_temp_table.append((processed_code, name, i))

            # 写入临时表（替代CSV文件）
            try:
                from 数据2_网络采集.人气临时表管理 import 获取临时表管理器
                temp_manager = 获取临时表管理器()
                temp_manager.写入东财数据(processed_data_for_temp_table)
                temp_manager.close()

                # 记录采集成功（写入临时表）
                logger.记录模块执行(f"网站数据采集完成", len(raw_stock_data))

                # 🔄 东财数据采集完成后，立即触发人气融合模块
                _trigger_popularity_fusion()

            except Exception as e:
                logger.记录错误(f"写入临时表失败", e)
                return None
        else:
            logger.记录错误(f"网站数据采集失败，未获取到数据")
    except Exception as e:
        logger.记录错误(f"程序异常", e)
        import traceback
        traceback.print_exc()


def run_continuous():
    """循环运行模式：按配置间隔执行"""
    cycle_count = 0
    success_count = 0
    fail_count = 0

    # logger.info(f"🔄 [人气_东财采集] 循环模式启动 - 60秒间隔")

    try:
        while True:
            cycle_count += 1
            start_time = time.time()

            try:
                main()
                success_count += 1
                # logger.info(f"💓 [人气_东财采集] 第{cycle_count}次执行成功")
            except Exception as e:
                fail_count += 1
                logger.记录错误(f"第{cycle_count}次执行失败", e)

            # 计算等待时间
            execution_time = time.time() - start_time
            sleep_time = max(0, EastmoneyConfig.CYCLE_INTERVAL - execution_time)

            if sleep_time > 0:
                time.sleep(sleep_time)
            else:
                logger.记录错误(f"执行时间{execution_time:.1f}秒超过{EastmoneyConfig.CYCLE_INTERVAL}秒间隔")

    except KeyboardInterrupt:
        # logger.info(f"<人气_东财采集> 用户中断，正在退出...")
        pass
    except Exception as e:
        logger.记录错误(f"循环运行异常", e)
    finally:
        # logger.info(f"💓 [人气_东财采集] 循环结束汇总 - 运行{cycle_count}个周期，{success_count}次成功，{fail_count}次失败")
        # 程序退出时清理浏览器资源
        SimpleBrowserManager.cleanup_shared_instance()


if __name__ == "__main__":
    import sys
    import atexit

    # 注册程序退出时的清理函数
    atexit.register(SimpleBrowserManager.cleanup_shared_instance)

    try:
        if len(sys.argv) > 1 and sys.argv[1] == "--once":
            # 单次执行模式
            main()
        else:
            # 循环执行模式（默认）
            run_continuous()
    except KeyboardInterrupt:
        # 用户中断时也清理浏览器
        SimpleBrowserManager.cleanup_shared_instance()
    except Exception:
        # 异常退出时也清理浏览器
        SimpleBrowserManager.cleanup_shared_instance()
        raise