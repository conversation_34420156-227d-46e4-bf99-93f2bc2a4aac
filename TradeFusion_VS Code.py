#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradeFusion 主启动器
支持VS Code调试和独立运行模式
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_colored(text, color='white'):
    """打印彩色文本 - 简化版本"""
    print(text)

def start_core_modules(project_root):
    """启动TradeFusion核心采集模块"""
    modules_started = 0

    # 定义核心采集模块
    core_modules = [
        {
            'name': '人气_东财采集',
            'path': '数据2_网络采集/人气_东财采集.py',
            'description': '东方财富人气数据采集'
        },
        {
            'name': '人气_同花采集',
            'path': '数据2_网络采集/人气_同花采集.py',
            'description': '同花顺人气数据采集'
        },
        {
            'name': '采集_本地数据',
            'path': '数据1_本地采集/采集_本地数据.py',
            'description': '本地DAT文件数据采集'
        },
        {
            'name': '选股宝抓取',
            'path': '数据3_网络采集_bk/选股宝抓取.py',
            'description': '选股宝数据网络采集'
        }
    ]

    print_colored("📋 正在启动核心采集模块:", 'cyan')
    venv_python = project_root / "venv" / "Scripts" / "python.exe"

    for i, module in enumerate(core_modules, 1):
        module_path = project_root / module['path']

        if module_path.exists():
            print_colored(f"   {i}. 启动 {module['name']}...", 'yellow')

            try:
                # 启动模块进程
                process = subprocess.Popen(
                    [str(venv_python), "-u", str(module_path)],
                    cwd=str(project_root),
                    creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0,
                    shell=False
                )

                print_colored(f"      ✅ 成功启动 (PID: {process.pid})", 'green')
                print_colored(f"      📝 {module['description']}", 'cyan')
                modules_started += 1

                # 如果不是最后一个模块，等待5秒避免竞争
                if i < len(core_modules):
                    print_colored(f"      ⏳ 等待5秒后启动下一个模块...", 'yellow')
                    time.sleep(5)

            except Exception as e:
                print_colored(f"      ❌ 启动失败: {e}", 'red')
        else:
            print_colored(f"   {i}. ❌ {module['name']}: 文件不存在", 'red')

        # 如果文件不存在也要等待，保持启动节奏一致
        if not module_path.exists() and i < len(core_modules):
            print_colored(f"      ⏳ 等待5秒后继续...", 'yellow')
            time.sleep(5)

    return modules_started

def start_tradefusion():
    """启动TradeFusion系统"""
    print_colored("🚀 正在启动TradeFusion系统...", 'green')

    try:
        project_root = Path(__file__).parent
        venv_python = project_root / "venv" / "Scripts" / "python.exe"

        # 设置工作目录
        os.chdir(project_root)

        print_colored("✅ 环境已准备就绪", 'green')
        print_colored("📍 项目根目录: " + str(project_root), 'yellow')
        print_colored("🐍 Python环境: " + str(venv_python), 'yellow')

        # 显示环境状态
        print_colored("\n📊 环境状态检查:", 'cyan')
        print_colored(f"   虚拟环境: {'✅ 正常' if venv_python.exists() else '❌ 异常'}", 'green' if venv_python.exists() else 'red')
        print_colored(f"   工作目录: {os.getcwd()}", 'yellow')
        print_colored(f"   Python版本: {sys.version.split()[0]}", 'yellow')

        # 检查关键目录
        print_colored("\n📁 关键目录检查:", 'cyan')
        key_dirs = ['公共模块', '数据2_网络采集', '数据库写大智慧', 'logs']
        for dir_name in key_dirs:
            dir_path = project_root / dir_name
            status = "✅ 存在" if dir_path.exists() else "❌ 缺失"
            print_colored(f"   {dir_name}: {status}", 'green' if dir_path.exists() else 'red')

        print_colored("\n✅ 环境检查完成", 'green')

        print_colored("\n🚀 启动TradeFusion核心采集模块...", 'cyan')

        # 启动核心采集模块
        modules_started = start_core_modules(project_root)

        if modules_started > 0:
            print_colored(f"✅ 成功启动 {modules_started} 个核心模块", 'green')
        else:
            print_colored("⚠️ 未启动任何模块（可能是调试模式）", 'yellow')

        return True

    except Exception as e:
        print_colored(f"❌ 启动失败: {e}", 'red')
        return False

def main():
    """主函数"""
    print_colored("🎯 TradeFusion 主启动器 v2.0", 'cyan')
    print_colored("支持VS Code调试和独立运行", 'cyan')
    print_colored("=" * 50, 'cyan')
    
    # 检查当前Python是否为虚拟环境Python
    project_root = Path(__file__).parent
    venv_python = project_root / "venv" / "Scripts" / "python.exe"
    current_python = Path(sys.executable)
    
    print_colored(f"当前Python: {current_python}", 'yellow')
    print_colored(f"虚拟环境Python: {venv_python}", 'yellow')
    
    try:
        if not current_python.samefile(venv_python):
            print_colored("❌ 请确保VS Code使用虚拟环境Python", 'red')
            print_colored("请按Ctrl+Shift+P，选择'Python: Select Interpreter'", 'yellow')
            print_colored(f"然后选择: {venv_python}", 'yellow')
            return
    except Exception:
        print_colored("⚠️  无法比较Python路径，继续启动", 'yellow')
    
    # 启动TradeFusion
    if start_tradefusion():
        print_colored("\n🎉 TradeFusion启动完成！", 'green')
    else:
        print_colored("\n❌ TradeFusion启动失败", 'red')

if __name__ == "__main__":
    main()
