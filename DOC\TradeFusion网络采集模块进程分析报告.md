# TradeFusion网络采集模块进程分析报告

## 概述
本报告分析了TradeFusion项目中三个主要网络采集模块的进程及其子进程情况，包括进程名称、地址和运行状态。

## 分析时间
- 报告生成时间：2025年7月31日 10:30
- 分析基于当前运行状态

## 模块进程分析

### 1. 人气_同花采集.py

#### 主进程信息
| 属性 | 值 |
|------|-----|
| **模块路径** | `E:\TradeFusion\数据2_网络采集\人气_同花采集.py` |
| **运行进程ID** | 12196, 20068 |
| **Python解释器** | `E:\TradeFusion\venv\Scripts\python.exe` |
| **运行状态** | 活跃运行中 |

#### 子进程分析
**浏览器管理器类型：** SimpleBrowserManager (Playwright)

**启动的子进程：**
- **浏览器类型：** Chromium
- **浏览器路径：** `E:\TradeFusion\browsers\chromium-1181\chrome-win\chrome.exe`
- **运行模式：** 无头模式 (headless=True)
- **进程特征：** 
  - 使用项目本地浏览器
  - 支持持久化连接
  - 自动清理机制

**目标网站：**
- **URL：** `https://eq.10jqka.com.cn/frontend/thsTopRank/index.html`
- **数据源：** 同花顺热榜

### 2. 人气_东财采集.py

#### 主进程信息
| 属性 | 值 |
|------|-----|
| **模块路径** | `E:\TradeFusion\数据2_网络采集\人气_东财采集.py` |
| **运行进程ID** | 9364, 17792 |
| **Python解释器** | `E:\TradeFusion\venv\Scripts\python.exe` |
| **运行状态** | 活跃运行中 |

#### 子进程分析
**浏览器管理器类型：** SimpleBrowserManager (Playwright)

**启动的子进程：**
- **浏览器类型：** Chromium
- **浏览器路径：** `E:\TradeFusion\browsers\chromium-1181\chrome-win\chrome.exe`
- **运行模式：** 无头模式 (headless=True)
- **进程特征：**
  - 使用项目本地浏览器
  - 支持持久化连接
  - 模拟用户行为（鼠标移动、滚轮）

**目标网站：**
- **URL：** `https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/stock`
- **数据源：** 东方财富热榜

### 3. 选股宝抓取.py

#### 主进程信息
| 属性 | 值 |
|------|-----|
| **模块路径** | `E:\TradeFusion\数据3_网络采集_bk\选股宝抓取.py` |
| **运行进程ID** | 22456, 21876 |
| **Python解释器** | `E:\TradeFusion\venv\Scripts\python.exe` |
| **运行状态** | 活跃运行中 |

#### 子进程分析
**浏览器管理器类型：** 持久化浏览器上下文 (Playwright)

**启动的子进程：**
- **浏览器类型：** Chromium
- **浏览器路径：** `E:\TradeFusion\browsers\chromium-1181\chrome-win\chrome.exe`
- **运行模式：** 无头模式 (headless=True)
- **用户数据目录：** `E:\TradeFusion\browser_data\xuangubao`
- **进程特征：**
  - 使用持久化用户数据目录
  - 反检测脚本
  - 时间控制机制（禁止8:40-9:24运行）

**目标网站：**
- **URL：** `https://xuangutong.com.cn/top-gainer`
- **数据源：** 选股宝涨幅榜

## Chrome子进程详细分析

### 当前运行的Chrome进程
| 进程ID | 父进程ID | 进程类型 | 说明 |
|--------|----------|----------|------|
| 17880 | 18668 | 主进程 | Chrome主浏览器进程 |
| 22364 | 17880 | crashpad-handler | 崩溃处理器 |
| 21728 | 17880 | gpu-process | GPU处理进程 |
| 21944 | 17880 | network-service | 网络服务进程 |
| 22084 | 17880 | storage-service | 存储服务进程 |
| 22244 | 17880 | renderer | 渲染进程1 |
| 29400 | 17880 | renderer | 渲染进程2 |
| 27504 | 17880 | renderer | 渲染进程3 |

### Chrome进程特征
- **用户数据目录：** `E:\TradeFusion\browser_data\xuangubao`
- **启动参数：** 包含反检测、无沙盒、中文语言等配置
- **调试端口：** 使用remote-debugging-pipe
- **安全配置：** 忽略证书错误、禁用各种安全检查

## 进程管理机制

### 1. 浏览器复用策略
- **共享实例：** 所有模块使用共享的浏览器管理器实例
- **持久化连接：** 浏览器进程在多次采集间保持活跃
- **资源清理：** 程序退出时自动清理浏览器资源

### 2. 进程监控
- **健康检查：** 定期检查浏览器进程可用性
- **异常恢复：** 浏览器失效时自动重新创建
- **内存管理：** 页面级别的资源清理

### 3. 安全机制
- **时间控制：** 选股宝模块在交易敏感时段禁止运行
- **错误处理：** 完善的异常捕获和重试机制
- **调试支持：** 错误时自动保存截图和页面源码

## 资源使用情况

### 内存占用
- **Python进程：** 每个采集模块约占用50-100MB
- **Chrome进程：** 主进程约占用200-300MB，子进程各占用50-150MB
- **总计：** 约1-2GB内存占用

### 网络连接
- **并发连接：** 每个模块维护独立的网络连接
- **连接复用：** 使用持久化连接减少握手开销
- **超时控制：** 60秒超时机制

## 内存占用对比分析

### 实际测试结果（2025年7月31日 10:45）

| 浏览器类型 | 进程数量 | 总内存占用 | 平均每进程内存 | 主要特征 |
|------------|----------|------------|----------------|----------|
| **chrome.exe** (选股宝) | 7个 | 188.98 MB | 27.00 MB | 持久化上下文，内存优化 |
| **headless_shell.exe** (同花/东财) | 8个 | 450.53 MB | 56.32 MB | 临时实例，内存占用较高 |

### 详细进程内存分布

**Chrome进程 (选股宝模块)：**
- PID 17880: 66.98 MB (主进程)
- PID 21728: 54.19 MB (GPU进程)
- PID 21944: 19.13 MB (网络服务)
- PID 22084: 7.88 MB (存储服务)
- PID 22244: 7.07 MB (渲染进程)
- PID 22364: 2.75 MB (崩溃处理)
- PID 28948: 30.98 MB (渲染进程)

**Headless_shell进程 (同花/东财模块)：**
- PID 6936: 180.34 MB (主进程)
- PID 20744: 134.12 MB (主进程)
- PID 15776: 39.19 MB (子进程)
- PID 19196: 27.27 MB (子进程)
- PID 17548: 20.94 MB (子进程)
- PID 13900: 18.77 MB (子进程)
- PID 16504: 15.13 MB (子进程)
- PID 6412: 14.77 MB (子进程)

### 关键发现

1. **chrome.exe方式确实内存占用更低**
   - 总内存占用少约58%
   - 平均每进程内存少约52%

2. **内存优化原因分析**
   - **持久化上下文**：避免重复初始化开销
   - **共享资源**：多个页面共享同一浏览器实例
   - **优化配置**：精细的启动参数和资源管理

3. **反爬虫严格程度修正**
   - **最严格：同花顺** (使用headless_shell，内存占用高)
   - **中等：东方财富** (使用headless_shell，内存占用高)
   - **最宽松：选股宝** (使用chrome.exe，内存占用低)

## 建议和优化

### 1. 性能优化建议
- **推荐统一使用chrome.exe方式**：内存占用更低，性能更好
- **实现持久化上下文**：为同花/东财模块也采用persistent_context
- **优化启动参数**：借鉴选股宝模块的配置参数

### 2. 代码优化方案
```python
# 建议同花/东财模块改为：
_global_browser = _global_playwright.chromium.launch_persistent_context(
    user_data_dir=str(BROWSER_USER_DATA_DIR),
    executable_path=str(LOCAL_BROWSER_PATH),
    headless=True,
    args=[
        "--no-sandbox",
        "--disable-blink-features=AutomationControlled",
        "--force-device-scale-factor=1",
        "--lang=zh-CN"
    ]
)
```

### 3. 监控改进
- 添加内存使用监控
- 实现进程健康检查
- 增加资源使用统计

### 4. 安全加固
- 根据网站反爬虫严格程度调整策略
- 为严格网站保留更多反检测机制
- 优化用户行为模拟

---

**报告生成者：** AURA-X AI  
**最后更新：** 2025年7月31日 10:30  
**版本：** 1.0
