//@charset "utf-8";
/*
 * 在jQuery或Zepto文件之后引入该js，若无亦可。
 * */

;(function (window, document, undefined) {
    "use strict";
    var defaults = {
        flags: false,
        float: false,
        closeBtn:['append','right'],
        thumb: false,
        thumburl: '',
        thumbheight: 0,
        thumbwidth: 0,
        code_script: false,
        m_tag: null,
        m_href : null,
        m_target: null,
        m_cont : null,
        staytime: null,
        appendId: null,
        style: {
            display: 'inline-block',
            width: 0,
            height: 0
        }
    };
    var refreshCount = 0;
    var firstEnter = true;
    var loaded = false;
    var $;
    function _cmsad(options){
        var CmsadObjName = 'CmsadObjName_' + options.adb_id;
        return window[CmsadObjName] = new Cmsad(options);
    }
    window.advertiseCallback = function(res1,res2){
        var result = arguments[0];
        var CmsadObjName = 'CmsadObjName_' + result.adb_id;
        window[CmsadObjName].options = $.extend({}, window[CmsadObjName].options, result || {});
        window[CmsadObjName].handleInfo();
        window[CmsadObjName].refreshTimer();
    }
    function Cmsad(options){
        var _this = this;
        if(firstEnter){
            firstEnter = false;
            this.firstEnter();
        }
        var timer = setInterval(function(){
            if(loaded && $){
                _this.init(options);
                clearInterval(timer);
            }
        },10);
    }
    //记得去掉注释！！！
    function setCookie(name, value, expires){
        var exp = new Date();
        exp.setTime(exp.getTime() + expires);
        document.cookie = name+'='
        + value
        +';expires='
        + exp.toGMTString()
        + ';path=/;domain='
        + window.location.host;
    }
    function getCookie(name) {
        var cookieValue = "";
        var search = name + "=";
        var offset,end;
        if (document.cookie.length > 0) {
            offset = document.cookie.indexOf(search);
            if (offset != -1)    {
                offset += search.length;
                end = document.cookie.indexOf(";", offset);
                if (end == -1) end = document.cookie.length;
                cookieValue = unescape(document.cookie.substring(offset, end))
            }
        }
        return cookieValue;
    }
    function userBrowser() {
        var ua = navigator.userAgent.toLowerCase();
        if (!!window.ActiveXObject && !window.XMLHttpRequest){
            return 'IE6';
        }else if(/msie/i.test(ua) && !/opera/.test(ua)){
            return 'IE';
        }else if(/firefox/i.test(ua)){
            return 'Firefox';
        }else if(/chrome/i.test(ua) && /webkit/i.test(ua) && /mozilla/i.test(ua)){
            return 'Chrome';
        }else if(/opera/i.test(ua)){
            return 'Opera';
        }else if(/webkit/i.test(ua) &&!(/chrome/i.test(ua) && /webkit/i.test(ua) && /mozilla/i.test(ua))){
            return 'Safari';
        }else{
            return 'Other';
        }
        return browser;
    }
    function userScreen(){
        return [screen.width,screen.height]
    }
    function userOS() {
        var ua = navigator.userAgent;
        var isWin = (navigator.platform == "Win32") || (navigator.platform == "Windows");
        var isMac = (navigator.platform == "Mac68K") || (navigator.platform == "MacPPC") || (navigator.platform == "Macintosh") || (navigator.platform == "MacIntel");
        if (isMac) return "Mac";
        var isUnix = (navigator.platform == "X11") && !isWin && !isMac;
        if (isUnix) return "Unix";
        var isLinux = (String(navigator.platform).indexOf("Linux") > -1);
        if (isLinux) return "Linux";
        if (isWin) {
            var isWin2K = ua.indexOf("Windows NT 5.0") > -1 || ua.indexOf("Windows 2000") > -1;
            if (isWin2K) return "Win2000";
            var isWinXP = ua.indexOf("Windows NT 5.1") > -1 || ua.indexOf("Windows XP") > -1;
            if (isWinXP) return "WinXP";
            var isWin2003 = ua.indexOf("Windows NT 5.2") > -1 || ua.indexOf("Windows 2003") > -1;
            if (isWin2003) return "Win2003";
            var isWinVista= ua.indexOf("Windows NT 6.0") > -1 || ua.indexOf("Windows Vista") > -1;
            if (isWinVista) return "WinVista";
            var isWin7 = ua.indexOf("Windows NT 6.1") > -1 || ua.indexOf("Windows 7") > -1;
            if (isWin7) return "Win7";
        }
        return "other";
    }
    function userDevices(){
        var ua = navigator.userAgent.toLowerCase();
        var bIsIpad = ua.match(/ipad/i) == 'ipad';
        var bIsIphoneOs = ua.match(/iphone os/i) == 'iphone os';
        var bIsMidp = ua.match(/midp/i) == 'midp';
        var bIsUc7 = ua.match(/rv:*******/i) == 'rv:*******';
        var bIsUc = ua.match(/ucweb/i) == 'ucweb';
        var bIsAndroid = ua.match(/android/i) == 'android';
        var bIsCE = ua.match(/windows ce/i) == 'windows ce';
        var bIsWM = ua.match(/windows mobile/i) == 'windows mobile';
        if (bIsIpad || bIsIphoneOs || bIsMidp || bIsUc7 || bIsUc || bIsAndroid || bIsCE || bIsWM) {
            return 'phone';
        } else {
            return 'pc';
        }
    }
    function userReferrer(){
        return document.referrer;
    }
    function isOnScreen(elem){
        var win = $(window);
        var viewport = {
            top : win.scrollTop(),
            left : win.scrollLeft()
        };
        viewport.right = viewport.left + win.width();
        viewport.bottom = viewport.top + win.height();

        var bounds = elem.offset();
        bounds.right = bounds.left + elem.outerWidth();
        bounds.bottom = bounds.top + elem.outerHeight();
        return (!(viewport.right < bounds.left || viewport.left > bounds.right || viewport.bottom < bounds.top || viewport.top > bounds.bottom));
    }
    function getJsonp(url){
        var $script = $('<script />');
        $script.attr({
            'src': url,
            'type': 'text/javascript'
        }).appendTo('body');
    }
    var resizeTimer = null;
    var resizeIdArr = [];
    function resize(){
        if($(window).width() <= 1024){
            for(var i = 0; i < resizeIdArr.length; i++){
                var elemId = resizeIdArr[i];
                var m_tag = $('#'+elemId).attr('data-tag')
                $('.cmsad_jump[data-tag='+m_tag+']').hide();
            }
        }else{
            for(var i = 0; i < resizeIdArr.length; i++){
                var elemId = resizeIdArr[i];
                var m_tag = $('#'+elemId).attr('data-tag')
                $('.cmsad_jump[data-tag='+m_tag+']').each(function(){
                    if($(this).is(':visible')){
                        $(this).show();
                    }
                })
            }
        }
    }
    Cmsad.prototype = {
        init: function(options){
            this.options = options = $.extend(true, {}, defaults, options || {});
            this.getInfo(this.options.adb_id);
        },
        firstEnter: function(){
            var _this = this;
            _this.defineSymbols();
            var timer = setInterval(function(){
                if(loaded && $){
                    clearInterval(timer);
                    _this.bindEvent();
                }
            },10);
        },
        refresh: function(){
            this.handleInfo();
        },
        /*获取广告信息*/
        getInfo: function(id){
            var _this = this;
            var url = '//ad.thsi.cn/block/' + id + '.js';
            getJsonp(url);
        },
        refreshTimer: function(){
            var _this = this;
            var options = _this.options;
            var adbid = options.adb_id;
            if(options.adb_refresh){
                var refreshtimer = 'cmsad_' + adbid + '_refresh_timer';
                var elem = $('#cmsad_' + adbid);
                window[refreshtimer] = setInterval(function(){
                    if(options.adb_refreshopt && isOnScreen(elem) && elem.is(':visible') || !options.adb_refreshopt && elem.is(':visible')){
                        clearInterval(window['cmsad_' + adbid + '_slide_timer']);
                        refreshCount++;
                        _this.refresh();
                    }
                },options.adb_refreshtime * 1000);
            }
        },
        handleInfo: function(){
            var _this = this;
            var options = _this.options;
            if(options.adb_stat){
                if(options.adb_freestat){
                                       /* if(options.adb_freeopt){
                    }else{
                    }*/
                //}else{
                    //广告位样式
                    if(options.adb_type == 2 && options.adb_float){
                        var adb_float = options.adb_float;
                        adb_float = eval('('+adb_float+')')
                        for(var i in adb_float){
                            adb_float[i] = parseInt(adb_float[i]);
                        }
                        options.style.zIndex = adb_float.zindex;
                        if(adb_float.follow){
                            options.style.position = 'fixed';
                            options.float = true;
                        }else{
                            options.style.position = 'absolute';
                        }
                        if(adb_float.displayrule){
                            var elemId = 'cmsad_'+_this.options.adb_id;
                            resizeIdArr = $.grep(resizeIdArr, function (item, i) {
                                return (item != elemId);
                            });
                            resizeIdArr.push(elemId);
                        }
                        options.staytime = adb_float.staytime ? adb_float.staytime * 1000 : null;
                        switch (parseInt(adb_float.positiontype)){
                            case 1:
                                options.style.bottom = adb_float.top || 0;
                                options.style.left = adb_float.left || 0;
                                options.closeBtn[0] = 'prepend';
                                break;
                            case 2:
                                options.style.bottom = adb_float.top || 0;
                                options.style.left = '50%';
                                options.style.marginLeft = -options.adb_width / 2;
                                options.closeBtn[0] = 'prepend';
                                break;
                            case 3:
                                options.style.bottom = adb_float.top || 0;
                                options.style.right = adb_float.left || 0;
                                options.closeBtn[0] = 'prepend';
                                break;
                            case 5:
                                options.style.top = adb_float.top || 0;
                                options.style.left = adb_float.left || 0;
                                options.closeBtn[1] = 'center';
                                options.flags = true;
                                break;
                            case 7:
                                options.style.top = adb_float.top || 0;
                                options.style.left = adb_float.left || 0;
                                break;
                            case 8:
                                options.style.top = adb_float.top || 0;
                                options.style.left = '50%';
                                options.style.marginLeft = -options.adb_width / 2;
                                break;
                            case 9:
                                options.style.top = adb_float.top || 0;
                                options.style.right = adb_float.right ||0;
                                break;
                                defaults:
                                    break;
                        }
                    }
                    //选择显示哪一个广告
                    var order = options.order,
                        arr = [],
                        tatol = 0,
                        random,
                        orderIdx,
                        orderCookieName;
                    for(var i = 0; i < order.length; i++){
                        var _thisorder,
                            level,
                            userCount;
                        _thisorder = order[i];
                        level = _thisorder.ado_level + 1;

                        if(_thisorder.ado_counttype > 0){
                            if(_thisorder.ado_counttype == 1){
                                orderCookieName = options.adb_id + '_' + _thisorder.ado_id + '_clickcount';
                            }else if(_thisorder.ado_counttype == 0){
                                orderCookieName = options.adb_id + '_' + _thisorder.ado_id + '_showcount';
                            }
                            userCount = parseInt(getCookie(orderCookieName));
                            userCount = isNaN(userCount) ? 0 : userCount;
                            if(_thisorder.ado_personday && _thisorder.ado_personday <= userCount){
                                continue;
                            }else if(_thisorder.ado_personhour && _thisorder.ado_personhour <= userCount){
                                continue;
                            }else if(_thisorder.ado_minutecount && _thisorder.ado_personminute <= userCount){
                                continue;
                            }
                        }

                        for(var j = 0; j < level; j++){
                            arr.push(i);
                        }
                        tatol += level;
                    }
                    random = Math.floor(Math.random()*tatol);
                    if(arr.length < 1){
                        return false;
                    }
                    orderIdx = arr[random];
                    options.orderIdx = orderIdx;
                    if(order[orderIdx].ado_counttype == 0){
                        userCount++;
                        setCountCookie(options.adb_id,userCount,0);
                    }
                    _this.showAdOrder(orderIdx);
                    //广告位统计
                    var taAdbid = 'thscmsgg_'+options.adb_id;
                    system.talog.push({id: taAdbid, _sid: taAdbid});
                }
            }else{
                return false;
            }
        },
        /*定义$符号*/
        defineSymbols: function(){
            var uDevices = userDevices();
            if(!window.Zepto && !window.jQuery){
                if(uDevices == 'phone'){
                    this.appendJs("//s.thsi.cn/js/zepto.min.js");
                }else if(uDevices == 'pc'){
                    this.appendJs("//s.thsi.cn/js/jquery-1.8.3.min.js");
                }
            }else{
                $ = window.Zepto || window.jQuery;
                loaded = true;
            }
            if(!window.TA){
                this.appendJs("//s.thsi.cn/js/ta.min.js");
            }
        },
        appendJs: function(src){
            var body = document.getElementsByTagName('body')[0];
            var script = document.createElement('script');
            script.type = 'text/javascript';
            script.onload = script.onreadystatechange = function() {
                if (!this.readyState || this.readyState === "loaded" || this.readyState === "complete" ) {

                    $ = window.jQuery || window.Zepto;
                    loaded = true;

                    script.onload = script.onreadystatechange = null;
                } };
            script.src = src;
            body.appendChild(script);
        },
        /*广告投放*/
        showAdOrder: function(index){
            this.getAdMaterial(index);
            //广告投放统计
            var _this = this;
            var order = _this.options.order[index];
            var taAdoid = 'thscmsgg_'+_this.options.adb_id + '_'+ order.ado_id;
            system.talog.push({id: taAdoid, _sid: taAdoid});
        },
        /*广告物料*/
        getAdMaterial: function(index){
            var _this = this;
            var adbid = _this.options.adb_id;
            var order = _this.options.order[index];
            var adoid = order.ado_id;
            var material = order.material;
            var materialIdx = 0;
            switch(order.ado_displaytype){
                case 0 :
                    var cookieAbokey = 'cmsad_' + adbid + '_' + index;
                    var cookieAboValue = parseInt(getCookie(cookieAbokey));
                    if(isNaN(cookieAboValue)){
                        cookieAboValue = 0;
                    }else{
                        cookieAboValue = cookieAboValue + 1 >= material.length ? 0 : cookieAboValue + 1;
                    }
                    setCookie(cookieAbokey, cookieAboValue, 1*24*60*60*1000);
                    materialIdx = cookieAboValue;
                    _this.showAdMaterial(adoid,material,materialIdx);
                    break;
                case 1 :
                    var cur = +new Date() / 1000;
                    var ratio;
                    if(order.ado_timetype){
                        var ado_date = order.ado_date;
                        ado_date = eval('('+ado_date+')');
                        for(var i = 0; i < ado_date.length; i++){
                            if(cur >= ado_date[i] && (i + 1 < ado_date.length ? cur <= ado_date[i + 1] : true)){
                                ratio = (i + 1) / ado_date.length - 0.01;
                                break;
                            }
                        }
                    }else{
                        var start = order.ado_starttime;
                        var end = order.ado_endtime;
                        ratio = (cur - start) / (end - start);
                    }
                    materialIdx =  Math.floor(material.length * ratio);
                    _this.showAdMaterial(adoid,material,materialIdx);
                    break;
                case 2 :
                    this.showAdMaterial(adoid,material,materialIdx);
                    var slideTimer = 'cmsad_' + adbid + '_slide_timer';
                    window[slideTimer] = setInterval(function() {
                        var CmsadObjName = 'CmsadObjName_' + adbid;
                        var adoid = window[CmsadObjName].options.adoid;
                        var material = window[CmsadObjName].options.material;
                        var materialIdx = window[CmsadObjName].options.index;
                        materialIdx = materialIdx >= material.length - 1 ? 0 : ++materialIdx;
                        window[CmsadObjName].showAdMaterial(adoid,material,materialIdx);
                    }, 2000);
                    break;
                default :
                    break;
            }
        },
        showAdMaterial: function(adoid,material,index){
            var adbid = this.options.adb_id;
            var thisMaterial = material[index];
            this.options.adoid = adoid;
            this.options.material = material;
            this.options.index = index;
            this.options.m_target = thisMaterial.adm_target == 1 ? '_self' : '_blank';
            this.options.m_tag = adbid + '_' + adoid + '_' +thisMaterial.adm_id;
            this.options.m_href = thisMaterial.adm_link;
            this.options.style.width = this.options.adb_width;
            this.options.style.height = this.options.adb_height;
            this.options.style.position = this.options.style.position;
            var m_cont = '';
            if(thisMaterial.adm_corner){
                var pos = '';
                switch (thisMaterial.adm_cornerpos){
                    case 1:
                        pos = 'left: 0;bottom: 0;';
                        break;
                    case 3:
                        pos = 'right: 0;bottom: 0;';
                        break;
                    case 7:
                        pos = 'left: 0;top: 0;';
                        break;
                    case 9:
                        pos = 'right: 0;top: 0;';
                        break;
                    default :
                        break;
                }
                m_cont += '<img src="' + thisMaterial.adc_url + '" style="position: absolute;' + pos +'width: ' + thisMaterial.adc_width + 'px;height: '+ thisMaterial.adc_height +'px;"/>';
            }else{
            }
            if(thisMaterial.adm_thumburl){
                this.options.thumb = true;
                this.options.thumburl = thisMaterial.adm_thumburl;
                this.options.thumbwidth = thisMaterial.adm_thumbwidth;
                this.options.thumbheight = thisMaterial.adm_thumbheight + 18;
            }
            switch (thisMaterial.adm_type){
                case 0 :
                    m_cont += '<img class="imgBig" src="' + thisMaterial.adm_url + '" style="display: block;"/>';
                    break;
                case 1 :
                    m_cont += thisMaterial.adm_text;
                    break;
                case 2 :
                    //flash待完成
                    break;
                case 3 :
                    if(thisMaterial.adm_code.indexOf('</script>') > -1){
                        this.options.code_script = true;
                    }
                    m_cont += thisMaterial.adm_code;
                    break;
                default :
                    break;
            }
            this.options.m_cont = m_cont;
            this.insertAdDom(adbid);
            var taAdmid = 'thscmsgg_'+this.options.m_tag;
            //广告物料统计
            system.talog.push({id: taAdmid, _sid: taAdmid});
        },
        insertAdDom: function(adbid){
            var _this = this;
            var options = _this.options;
            var style = options.style;
            var m_tag = options.m_tag;
            var m_href = options.m_href;
            var m_target = options.m_target;
            var m_cont = options.m_cont;
            var html = '<a href="javascript: void(0);" id="cmsad_'+ adbid +'" class="cmsad_jump" adbwidth='+options.adb_width+' adbheight='+options.adb_height+'>' + m_cont + '</a>';
            //IE6样式
            if(userBrowser() == 'IE6' && style.position == 'fixed' ){
                return false;
            }
            //广告位样式
            if($('#cmsad_' + adbid).length < 1){
                if(options.appendId){
                    $('#'+options.appendId).append(html);
                }else{
                    $('body').append(html);
                }
            }
            for(var i in style){
                $('#cmsad_' + adbid).css(i,style[i]);
            }
            $('#cmsad_' + adbid)
                .attr('data-tag',m_tag)
                .attr('data-href',m_href)
                .attr('data-target',m_target)
                .html('')
                .append(m_cont).show();
            //富媒体script特殊处理
            if(options.code_script){
                var elemid = options.appendId ? options.appendId : 'cmsad_' + adbid;
                _this.insertScriptDom(adbid,elemid,m_cont);
            }
            //浮层特殊处理
            if(options.float){
                var $closeBtn = '<p class="cmsad_close" style="line-height: 18px;text-align: '+options.closeBtn[1]+';">关闭</p>';
                //$('#cmsad_' + adbid).css('height',options.style.height+18);
                $('#cmsad_' + adbid).attr({
                    thumbwidth: options.thumbwidth,
                    thumbheight: options.thumbheight,
                    adbheight: options.adbheight + 18
                }).css({
                    width: options.adbbwidth,
                    height: options.adbheight
                });
                if(options.closeBtn[0] == 'append'){
                    $('#cmsad_' + adbid).append($closeBtn);
                }else{
                    $('#cmsad_' + adbid).prepend($closeBtn);
                }
                //挂旗特殊处理
                if(options.flags){
                    $('#cmsad_' + adbid + '_right').remove();
                    $('#cmsad_' + adbid).clone()
                        .attr('id','cmsad_' + adbid + '_right')
                        .css({
                            left: 'auto',
                            right: '0'
                        })
                        .insertAfter($('#cmsad_' + adbid));
                }
                if(options.thumb){
                    var thumbTimer = 'cmsad_' + adbid + '_thumb_timer';
                    var tag = $('#cmsad_' + adbid).attr('data-tag');
                    $('.cmsad_jump[data-tag='+tag+']').each(function(){
                        $(this).find('img.imgSmall').remove();
                        $('<img class="imgSmall" src="' + options.thumburl + '" style="display: none;"/>').insertAfter($(this).find('img'));
                        if($(this).attr('data-showtype') == 'thumb'){
                            showThumb(tag);
                        }else{
                            showNorm(tag);
                        }
                    });

                    if(!window[thumbTimer]){
                        window[thumbTimer] = setTimeout(function(){
                            var tag = $('#cmsad_' + adbid).attr('data-tag');
                            showThumb(tag);
                            clearTimeout(thumbTimer);
                        },3000);
                    }
                }else{
                    _this.setStaytime();
                }
            }else{
                _this.setStaytime();
            }
            resize();
            //原统计
            //TA.log({id: 'cmsad_'+m_tag, tid: 'cmsad_'+m_tag});
        },
        insertScriptDom: function(adbid,elemid,html){
            /* if(location.href.indexOf('cmsad_iframe') > -1){*/
            var cont = document.getElementById(elemid);
            cont.innerHTML = html;
            for(var i = 0; i < cont.getElementsByTagName('script').length; i++){
                var oldScript = cont.getElementsByTagName('script')[0];
                cont.removeChild(oldScript);
                var newScript = document.createElement('script');
                newScript.type = 'text/javascript';
                if(oldScript.innerHTML){
                    newScript.innerHTML = oldScript.innerHTML;
                }
                if(oldScript.src){
                    newScript.src = oldScript.src;
                }
                cont.appendChild(newScript);
            }
            /*}else{
             html = html.replace(/&/g,"&amp;");
             html = html.replace(/</g,"&lt;");
             html = html.replace(/>/g,"&gt;");
             html = html.replace(/\"/g,"&quot;");
             html = html.replace(/\'/g,"&apos;");
             $('#'+elemid).html('<iframe src="cmsad_iframe.html?adbid='+adbid+'" width="'+this.options.style.width+'" height="'+this.options.style.height+'" frameborder="0" srcdoc="'+html+'"></iframe>')
             }*/

        },
        setStaytime: function(){
            var options = this.options;
            var adbid = options.adb_id;
            if(options.staytime){
                var domtimer = 'cmsad_' + adbid + '_timer';
                window[domtimer] = setTimeout(function(){
                    var tag = $('#cmsad_' + adbid).attr('data-tag');
                    $('.cmsad_jump[data-tag='+tag+']').hide();
                    clearTimeout(domtimer);
                },options.staytime);
            }
        },
        bindEvent: function(){
            $(document).on('click','.cmsad_close',function(){
                var m_tag = $(this).parents('.cmsad_jump').attr('data-tag');
                $('.cmsad_jump[data-tag='+m_tag+']').hide();
                return false;
            });
            $(document).on('click','.cmsad_jump',function(){
                var m_target = $(this).attr('data-target');
                var m_tag = $(this).attr('data-tag');
                var href = $(this).attr('data-href');
                var orderCookieName = m_tag.split('_')[0] + '_' + m_tag.split('_')[1] + '_clickcount';
                var userCount = parseInt(getCookie(orderCookieName));
                userCount = isNaN(userCount) ? 0 : userCount;
                userCount++;
                setCountCookie(m_tag.split('_')[0],userCount,1);
                var taClickid = 'thscmsgg_'+m_tag+'_click';
                TA.log({id: taClickid, _sid: taClickid});
                if(m_target == '_blank'){
                    window.open(href);
                }else{
                    location.href = href;
                }
            });
            $(document).on('mouseover','.cmsad_jump', function(e){
                if($(this).find('.imgSmall').length > 0 && $(e.target).closest('.cmsad_close').length < 1){
                    var m_tag = $(this).attr('data-tag');
                    showNorm(m_tag);
                }
            });
            $(document).on('mouseleave','.cmsad_jump', function(){
                if($(this).find('.imgSmall').length > 0) {
                    var m_tag = $(this).attr('data-tag');
                    showThumb(m_tag);
                }
            });
            $(window).resize(function(){
                if (resizeTimer) clearTimeout(resizeTimer);
                resizeTimer = setTimeout(resize, 500);
            });
        }
    }
    function showThumb(tag){
        $('.cmsad_jump[data-tag='+tag+']').each(function(){
            $(this).css({
                width: $(this).attr('thumbwidth'),
                height: $(this).attr('thumbheight')
            });
            $(this).attr('data-showtype','thumb');
        });
        $('.cmsad_jump[data-tag='+tag+']').find('.imgBig').hide();
        $('.cmsad_jump[data-tag='+tag+']').find('.imgSmall').show();
    }
    function showNorm(tag){
        $('.cmsad_jump[data-tag='+tag+']').each(function(){
            $(this).css({
                width: $(this).attr('adbwidth'),
                height: $(this).attr('adbheight')
            });
            $(this).attr('data-showtype','');
        });
        $('.cmsad_jump[data-tag='+tag+']').find('.imgBig').show();
        $('.cmsad_jump[data-tag='+tag+']').find('.imgSmall').hide();
    }
    function setCountCookie(adbid,userCount,counttype){
        var CmsadObjName = 'CmsadObjName_' + adbid;
        var options = window[CmsadObjName].options;
        var order = options.order;
        var nowOrder = order[options.orderIdx];
        var orderCookieName;
        if(counttype == 0){
            orderCookieName = options.adb_id + '_' + nowOrder.ado_id + '_showcount';
        }else if(counttype == 1){
            orderCookieName = options.adb_id + '_' + nowOrder.ado_id + '_clickcount';
        }else{
            return false;
        }

        var x = new Date();
        var y = new Date();
        if(nowOrder.ado_personday){
            x.setHours(0,0,0,0);
            var exp = 24*3600*1000-(y.getTime()-x.getTime());
            setCookie(orderCookieName,userCount,exp);
        }else if(nowOrder.ado_personhour){
            x.setMinutes(0,0,0);
            var exp = 3600*1000-(y.getTime()-x.getTime());
            setCookie(orderCookieName,userCount,exp);
        }else if(nowOrder.ado_minutecount){
            x.setSeconds(0,0);
            var exp = nowOrder.ado_minutecount*60*1000-(y.getTime()-x.getTime());
            setCookie(orderCookieName,userCount,nowOrder.exp);
        }
    }
    window._cmsad = window._cmsad || _cmsad;
})(window, document);
